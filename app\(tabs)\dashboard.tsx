import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { router } from 'expo-router';
import { ScreenWrapper } from '@/components/ui/ScreenWrapper';
import { Card } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import {
  useGetManagerStatsQuery,
  useGetTeamAbsencesQuery,
  useGetPendingLeaveRequestsQuery,
} from '@/store/api/apiSlice';
import {
  CircleAlert as AlertCircle,
  Users,
  Calendar,
  TrendingUp,
  Clock,
} from 'lucide-react-native';

export default function ManagerDashboard() {
  // RTK Query hooks
  const {
    data: statsData,
    isLoading: isLoadingStats,
    error: statsError,
  } = useGetManagerStatsQuery();

  const { data: absencesData, isLoading: isLoadingAbsences } =
    useGetTeamAbsencesQuery();

  const { data: pendingData, isLoading: isLoadingPending } =
    useGetPendingLeaveRequestsQuery({});

  const todayStats = statsData?.todayStats || {
    totalEmployees: 0,
    onLeave: 0,
    absent: 0,
    present: 0,
  };
  const weeklyStats = statsData?.weeklyStats || {
    attendanceRate: 0,
    avgHoursWorked: '0h',
  };
  const teamAbsences = absencesData || { onLeave: [], absent: [] };
  const pendingRequests = pendingData?.data || [];

  const handleNavigateToApprovals = () => {
    router.push('/(tabs)/approvals');
  };

  // Show loading state while fetching critical data
  if (isLoadingStats || isLoadingPending) {
    return (
      <ScreenWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3b82f6" />
          <Text style={styles.loadingText}>
            Chargement du tableau de bord...
          </Text>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Tableau de Bord</Text>
          <Text style={styles.subtitle}>Vue d'ensemble de votre équipe</Text>
        </View>

        <Card style={styles.alertCard}>
          <TouchableOpacity
            style={styles.alertContent}
            onPress={handleNavigateToApprovals}
          >
            <AlertCircle size={24} color="#F59E0B" />
            <View style={styles.alertText}>
              <Text style={styles.alertTitle}>Actions Requises</Text>
              <Text style={styles.alertDescription}>
                {pendingRequests.length} demandes de congé en attente
              </Text>
            </View>
            <Badge variant="warning" text={pendingRequests.length.toString()} />
          </TouchableOpacity>
        </Card>

        <View style={styles.statsGrid}>
          <Card style={styles.statCard}>
            <View style={styles.statContent}>
              <Users size={24} color="#3B82F6" />
              <Text style={styles.statValue}>{todayStats.totalEmployees}</Text>
              <Text style={styles.statLabel}>Total Employés</Text>
            </View>
          </Card>

          <Card style={styles.statCard}>
            <View style={styles.statContent}>
              <Calendar size={24} color="#EF4444" />
              <Text style={styles.statValue}>{todayStats.onLeave}</Text>
              <Text style={styles.statLabel}>En Congé</Text>
            </View>
          </Card>

          <Card style={styles.statCard}>
            <View style={styles.statContent}>
              <Clock size={24} color="#F59E0B" />
              <Text style={styles.statValue}>{todayStats.absent}</Text>
              <Text style={styles.statLabel}>Absents</Text>
            </View>
          </Card>

          <Card style={styles.statCard}>
            <View style={styles.statContent}>
              <TrendingUp size={24} color="#10B981" />
              <Text style={styles.statValue}>
                {weeklyStats.attendanceRate}%
              </Text>
              <Text style={styles.statLabel}>Assiduité</Text>
            </View>
          </Card>
        </View>

        <Card style={styles.sectionCard}>
          <Text style={styles.sectionTitle}>Aujourd'hui</Text>

          <View style={styles.todaySection}>
            <Text style={styles.subsectionTitle}>Employés en congé</Text>
            {teamAbsences.onLeave.length > 0 ? (
              teamAbsences.onLeave.map((employee, index) => (
                <View key={index} style={styles.employeeRow}>
                  <View style={styles.avatar}>
                    <Text style={styles.avatarText}>
                      {employee.firstName[0]}
                      {employee.lastName[0]}
                    </Text>
                  </View>
                  <View style={styles.employeeInfo}>
                    <Text style={styles.employeeName}>
                      {employee.firstName} {employee.lastName}
                    </Text>
                    <Text style={styles.employeeStatus}>
                      {employee.leaveType} - Retour le {employee.returnDate}
                    </Text>
                  </View>
                </View>
              ))
            ) : (
              <Text style={styles.emptyText}>
                Aucun employé en congé aujourd'hui
              </Text>
            )}
          </View>

          <View style={styles.todaySection}>
            <Text style={styles.subsectionTitle}>
              Employés absents (non pointés)
            </Text>
            {teamAbsences.absent.length > 0 ? (
              teamAbsences.absent.map((employee, index) => (
                <View key={index} style={styles.employeeRow}>
                  <View style={styles.avatar}>
                    <Text style={styles.avatarText}>
                      {employee.firstName[0]}
                      {employee.lastName[0]}
                    </Text>
                  </View>
                  <View style={styles.employeeInfo}>
                    <Text style={styles.employeeName}>
                      {employee.firstName} {employee.lastName}
                    </Text>
                    <Text style={styles.employeeStatus}>
                      Aucun pointage aujourd'hui
                    </Text>
                  </View>
                </View>
              ))
            ) : (
              <Text style={styles.emptyText}>Tous les employés ont pointé</Text>
            )}
          </View>
        </Card>
      </ScrollView>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#111827',
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  alertCard: {
    margin: 16,
    backgroundColor: '#FEF3C7',
    borderColor: '#F59E0B',
    borderWidth: 1,
  },
  alertContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  alertText: {
    flex: 1,
    marginLeft: 12,
  },
  alertTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#92400E',
  },
  alertDescription: {
    fontSize: 14,
    color: '#A16207',
    marginTop: 2,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    gap: 12,
  },
  statCard: {
    width: '48%',
    minHeight: 100,
  },
  statContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#111827',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
    textAlign: 'center',
  },
  sectionCard: {
    margin: 16,
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  todaySection: {
    marginBottom: 20,
  },
  subsectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  employeeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#3B82F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  employeeInfo: {
    marginLeft: 12,
    flex: 1,
  },
  employeeName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  employeeStatus: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  emptyText: {
    fontSize: 14,
    color: '#9ca3af',
    fontStyle: 'italic',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
    fontWeight: '500',
  },
});
