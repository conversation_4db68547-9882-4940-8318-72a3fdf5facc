import { useEffect, useState } from 'react';
import { Tabs, router } from 'expo-router';
import {
  Chrome as Home,
  Calendar,
  Clock,
  User,
  ChartBar as BarChart3,
  CircleCheck as CheckCircle,
  Users,
  Settings,
  Globe,
} from 'lucide-react-native';
import { useAppSelector } from '@/store/store';
import {
  selectCurrentUser,
  selectIsAuthenticated,
} from '@/store/slices/authSlice';
import { Badge } from '@/components/ui/Badge';

export default function TabLayout() {
  const user = useAppSelector(selectCurrentUser);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const [navigationReady, setNavigationReady] = useState(false);

  useEffect(() => {
    // Mark navigation as ready after a short delay
    const timer = setTimeout(() => {
      setNavigationReady(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated && navigationReady) {
      const timer = setTimeout(() => {
        router.replace('/login');
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, navigationReady]);

  const getTabsForRole = () => {
    if (user?.role === 'admin') {
      return [
        { name: 'index', title: 'Accueil', icon: Home },
        { name: 'dashboard', title: 'Tableau de Bord', icon: BarChart3 },
        { name: 'users', title: 'Utilisateurs', icon: Users },
        { name: 'analytics', title: 'Analytics', icon: BarChart3 },
        { name: 'settings', title: 'Paramètres', icon: Settings },
        { name: 'profile', title: 'Profil', icon: User },
      ];
    } else if (user?.role === 'manager') {
      return [
        { name: 'index', title: 'Accueil', icon: Home },
        { name: 'dashboard', title: 'Tableau de Bord', icon: BarChart3 },
        { name: 'approvals', title: 'Approbations', icon: CheckCircle },
        { name: 'leave', title: 'Congés', icon: Calendar },
        { name: 'history', title: 'Historique', icon: Clock },
        { name: 'profile', title: 'Mon Profil', icon: User },
      ];
    } else {
      return [
        { name: 'index', title: 'Accueil', icon: Home },
        { name: 'leave', title: 'Mes Congés', icon: Calendar },
        { name: 'history', title: 'Historique', icon: Clock },
        { name: 'profile', title: 'Profil', icon: User },
      ];
    }
  };

  const tabs = getTabsForRole();

  // Don't render tabs if user is not authenticated
  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          backgroundColor: '#ffffff',
          borderTopWidth: 1,
          borderTopColor: '#e5e7eb',
          height: 88,
          paddingTop: 8,
          paddingBottom: 34,
        },
        tabBarActiveTintColor: '#3B82F6',
        tabBarInactiveTintColor: '#6b7280',
      }}
    >
      {tabs.map((tab) => (
        <Tabs.Screen
          key={tab.name}
          name={tab.name}
          options={{
            title: tab.title,
            tabBarIcon: ({ size, color }) => (
              <tab.icon size={size} color={color} />
            ),
          }}
        />
      ))}
    </Tabs>
  );
}
