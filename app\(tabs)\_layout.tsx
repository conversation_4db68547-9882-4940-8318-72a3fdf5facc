import { useEffect, useState } from 'react';
import { Tabs, router } from 'expo-router';
import {
  House as Home,
  Calendar,
  Clock,
  User,
  ChartBar as BarChart3,
  CircleCheck as CheckCircle,
  Users,
  Settings,
  Globe,
} from 'lucide-react-native';
import { useAppSelector } from '@/store/store';
import {
  selectCurrentUser,
  selectIsAuthenticated,
} from '@/store/slices/authSlice';
import { Badge } from '@/components/ui/Badge';

export default function TabLayout() {
  const user = useAppSelector(selectCurrentUser);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const [navigationReady, setNavigationReady] = useState(false);

  useEffect(() => {
    // Mark navigation as ready after a short delay
    const timer = setTimeout(() => {
      setNavigationReady(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated && navigationReady) {
      const timer = setTimeout(() => {
        router.replace('/login');
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, navigationReady]);

  // Helper function to check if user can access a tab
  const canAccessTab = (tabRoles: string[]) => {
    const userRole = user?.role || 'employee';
    return tabRoles.includes(userRole);
  };

  // Don't render tabs if user is not authenticated
  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          backgroundColor: '#ffffff',
          borderTopWidth: 1,
          borderTopColor: '#e5e7eb',
          height: 88,
          paddingTop: 8,
          paddingBottom: 34,
        },
        tabBarActiveTintColor: '#3B82F6',
        tabBarInactiveTintColor: '#6b7280',
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Accueil',
          tabBarIcon: ({ size, color }) => <Home size={size} color={color} />,
        }}
      />

      <Tabs.Screen
        name="dashboard"
        options={{
          title: 'Dashboard',
          tabBarIcon: ({ size, color }) => (
            <BarChart3 size={size} color={color} />
          ),
          href: canAccessTab(['manager', 'admin']) ? '/dashboard' : null,
        }}
      />

      <Tabs.Screen
        name="approvals"
        options={{
          title: 'Approbations',
          tabBarIcon: ({ size, color }) => (
            <CheckCircle size={size} color={color} />
          ),
          href: canAccessTab(['manager', 'admin']) ? '/approvals' : null,
        }}
      />

      <Tabs.Screen
        name="users"
        options={{
          title: 'Utilisateurs',
          tabBarIcon: ({ size, color }) => <Users size={size} color={color} />,
          href: canAccessTab(['admin']) ? '/users' : null,
        }}
      />

      <Tabs.Screen
        name="analytics"
        options={{
          title: 'Analytics',
          tabBarIcon: ({ size, color }) => (
            <BarChart3 size={size} color={color} />
          ),
          href: canAccessTab(['admin']) ? '/analytics' : null,
        }}
      />

      <Tabs.Screen
        name="settings"
        options={{
          title: 'Paramètres',
          tabBarIcon: ({ size, color }) => (
            <Settings size={size} color={color} />
          ),
          href: canAccessTab(['admin']) ? '/settings' : null,
        }}
      />

      <Tabs.Screen
        name="leave"
        options={{
          title: 'Congés',
          tabBarIcon: ({ size, color }) => (
            <Calendar size={size} color={color} />
          ),
        }}
      />

      <Tabs.Screen
        name="history"
        options={{
          title: 'Historique',
          tabBarIcon: ({ size, color }) => <Clock size={size} color={color} />,
        }}
      />

      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profil',
          tabBarIcon: ({ size, color }) => <User size={size} color={color} />,
        }}
      />
    </Tabs>
  );
}
