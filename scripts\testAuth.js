// Simple test script to verify mock authentication data
const mockUsers = require('../data/mockUsers.json');

console.log('=== MOCK AUTHENTICATION TEST ===\n');

console.log('Available test accounts:');
console.log('1. Admin: <EMAIL> / password');
console.log('2. Manager: <EMAIL> / password');
console.log('3. Employee: <EMAIL> / password\n');

// Test authentication logic
function testLogin(email, password) {
  console.log(`Testing login: ${email} / ${password}`);
  
  const userData = mockUsers.users[email];
  
  if (!userData) {
    console.log('❌ User not found');
    return false;
  }
  
  if (password !== mockUsers.credentials.defaultPassword) {
    console.log('❌ Invalid password');
    return false;
  }
  
  console.log('✅ Login successful');
  console.log(`   User: ${userData.firstName} ${userData.lastName}`);
  console.log(`   Role: ${userData.role}`);
  console.log(`   Department: ${userData.department}\n`);
  return true;
}

// Test all accounts
testLogin('<EMAIL>', 'password');
testLogin('<EMAIL>', 'password');
testLogin('<EMAIL>', 'password');
testLogin('<EMAIL>', 'password');
testLogin('<EMAIL>', 'wrongpassword');

console.log('=== TEST COMPLETE ===');
