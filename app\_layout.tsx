import { useEffect } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Provider } from 'react-redux';
import { store } from '@/store/store';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { AuthService } from '@/services/authService';

function RootLayoutNav() {
  useFrameworkReady();

  useEffect(() => {
    // Initialize authentication state from storage
    AuthService.initializeAuth();
  }, []);

  return (
    <>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />
    </>
  );
}

export default function RootLayout() {
  return (
    <Provider store={store}>
      <RootLayoutNav />
    </Provider>
  );
}
