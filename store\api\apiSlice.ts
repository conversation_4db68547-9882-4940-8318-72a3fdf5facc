import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { RootState } from '../store';
import {
  User,
  LoginCredentials,
  LoginResponse,
  AttendanceRecord,
  ClockActionPayload,
  AttendanceHistory,
  AttendanceStats,
  LeaveRequest,
  CreateLeaveRequest,
  LeaveBalance,
  ApprovalAction,
  ApiResponse,
  PaginatedResponse,
} from '@/types';

// Define the API base URL - this should be set in your environment variables
const API_BASE_URL =
  process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000/api';

// Mock data for development
const mockUsers = {
  '<EMAIL>': {
    id: '1',
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    role: 'admin' as const,
    department: 'IT',
    hireDate: '2023-01-01',
  },
  '<EMAIL>': {
    id: '2',
    firstName: 'Manager',
    lastName: 'User',
    email: '<EMAIL>',
    role: 'manager' as const,
    department: 'HR',
    hireDate: '2023-02-01',
  },
  '<EMAIL>': {
    id: '3',
    firstName: 'Employee',
    lastName: 'User',
    email: '<EMAIL>',
    role: 'employee' as const,
    department: 'Sales',
    hireDate: '2023-03-01',
  },
};

// Mock API function for development
const mockApiCall = <T>(data: T, delay = 1000): Promise<T> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(data), delay);
  });
};

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Add authentication token to requests
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('content-type', 'application/json');
      return headers;
    },
  }),
  tagTypes: ['User', 'Attendance', 'Leave', 'Team', 'Notification'],
  endpoints: (builder) => ({
    // Authentication endpoints
    login: builder.mutation<LoginResponse, LoginCredentials>({
      queryFn: async (credentials) => {
        try {
          // Mock authentication logic
          const user = mockUsers[credentials.email as keyof typeof mockUsers];

          if (!user || credentials.password !== 'password') {
            return {
              error: {
                status: 401,
                data: { message: 'Email ou mot de passe incorrect' },
              },
            };
          }

          const response = await mockApiCall({
            user,
            token: `mock-token-${user.id}`,
            refreshToken: `mock-refresh-token-${user.id}`,
          });

          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: 'Erreur serveur' },
            },
          };
        }
      },
    }),

    logout: builder.mutation<void, void>({
      query: () => ({
        url: '/auth/logout',
        method: 'POST',
      }),
      invalidatesTags: ['User', 'Attendance', 'Leave'],
    }),

    // User profile endpoints
    getProfile: builder.query<User, void>({
      query: () => '/user/profile',
      providesTags: ['User'],
    }),

    updateProfile: builder.mutation<User, Partial<User>>({
      query: (updates) => ({
        url: '/user/profile',
        method: 'PUT',
        body: updates,
      }),
      invalidatesTags: ['User'],
    }),

    // Attendance endpoints
    getAttendanceStatus: builder.query<AttendanceRecord | null, void>({
      queryFn: async () => {
        try {
          const mockRecord: AttendanceRecord = {
            id: '1',
            userId: '3',
            date: new Date().toISOString().split('T')[0],
            clockIn: '09:01',
            clockOut: null,
            status: 'present',
            totalTime: null,
          };

          const response = await mockApiCall(mockRecord, 500);
          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: 'Erreur lors de la récupération du statut' },
            },
          };
        }
      },
      providesTags: ['Attendance'],
    }),

    clockIn: builder.mutation<AttendanceRecord, ClockActionPayload>({
      queryFn: async (payload) => {
        try {
          const mockRecord: AttendanceRecord = {
            id: '1',
            userId: '3',
            date: new Date().toISOString().split('T')[0],
            clockIn: new Date().toLocaleTimeString('fr-FR', {
              hour: '2-digit',
              minute: '2-digit',
            }),
            clockOut: null,
            status: 'present',
            totalTime: null,
            location: payload.location,
            notes: payload.notes,
          };

          const response = await mockApiCall(mockRecord, 800);
          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: "Erreur lors du pointage d'entrée" },
            },
          };
        }
      },
      invalidatesTags: ['Attendance'],
    }),

    clockOut: builder.mutation<AttendanceRecord, ClockActionPayload>({
      queryFn: async (payload) => {
        try {
          const mockRecord: AttendanceRecord = {
            id: '1',
            userId: '3',
            date: new Date().toISOString().split('T')[0],
            clockIn: '09:01',
            clockOut: new Date().toLocaleTimeString('fr-FR', {
              hour: '2-digit',
              minute: '2-digit',
            }),
            status: 'present',
            totalTime: '8h 30min',
            location: payload.location,
            notes: payload.notes,
          };

          const response = await mockApiCall(mockRecord, 800);
          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: 'Erreur lors du pointage de sortie' },
            },
          };
        }
      },
      invalidatesTags: ['Attendance'],
    }),

    getAttendanceHistory: builder.query<
      AttendanceHistory,
      { month?: number; year?: number }
    >({
      query: ({ month, year } = {}) => {
        const params = new URLSearchParams();
        if (month) params.append('month', month.toString());
        if (year) params.append('year', year.toString());
        return `/attendance/history?${params.toString()}`;
      },
      providesTags: ['Attendance'],
    }),

    getAttendanceStats: builder.query<AttendanceStats, void>({
      query: () => '/attendance/stats',
      providesTags: ['Attendance'],
    }),

    // Leave request endpoints
    getLeaveRequests: builder.query<
      PaginatedResponse<LeaveRequest>,
      { page?: number; status?: string }
    >({
      queryFn: async ({ page = 1, status } = {}) => {
        try {
          const mockRequests: LeaveRequest[] = [
            {
              id: '1',
              userId: '3',
              type: 'vacation',
              startDate: '2024-03-15',
              endDate: '2024-03-19',
              totalDays: 5,
              status: 'pending',
              reason: 'Vacances familiales',
              comment: 'Voyage prévu depuis longtemps',
              createdAt: '2024-03-01T10:00:00Z',
              updatedAt: '2024-03-01T10:00:00Z',
            },
            {
              id: '2',
              userId: '3',
              type: 'sick',
              startDate: '2024-02-20',
              endDate: '2024-02-21',
              totalDays: 2,
              status: 'approved',
              reason: 'Grippe',
              approvedBy: '2',
              approvedAt: '2024-02-19T14:30:00Z',
              createdAt: '2024-02-19T09:00:00Z',
              updatedAt: '2024-02-19T14:30:00Z',
            },
          ];

          let filteredRequests = mockRequests;
          if (status) {
            filteredRequests = mockRequests.filter(
              (req) => req.status === status
            );
          }

          const response = await mockApiCall({
            data: filteredRequests,
            pagination: {
              page,
              limit: 10,
              total: filteredRequests.length,
              totalPages: Math.ceil(filteredRequests.length / 10),
            },
          });

          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: 'Erreur lors de la récupération des demandes' },
            },
          };
        }
      },
      providesTags: ['Leave'],
    }),

    createLeaveRequest: builder.mutation<LeaveRequest, CreateLeaveRequest>({
      queryFn: async (request) => {
        try {
          const newRequest: LeaveRequest = {
            id: Date.now().toString(),
            userId: '3',
            ...request,
            totalDays:
              Math.ceil(
                (new Date(request.endDate).getTime() -
                  new Date(request.startDate).getTime()) /
                  (1000 * 60 * 60 * 24)
              ) + 1,
            status: 'pending',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          const response = await mockApiCall(newRequest, 1000);
          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: 'Erreur lors de la création de la demande' },
            },
          };
        }
      },
      invalidatesTags: ['Leave'],
    }),

    updateLeaveRequest: builder.mutation<
      LeaveRequest,
      { id: string; updates: Partial<CreateLeaveRequest> }
    >({
      query: ({ id, updates }) => ({
        url: `/leave/requests/${id}`,
        method: 'PUT',
        body: updates,
      }),
      invalidatesTags: ['Leave'],
    }),

    cancelLeaveRequest: builder.mutation<void, string>({
      query: (id) => ({
        url: `/leave/requests/${id}/cancel`,
        method: 'POST',
      }),
      invalidatesTags: ['Leave'],
    }),

    getLeaveBalance: builder.query<LeaveBalance, void>({
      query: () => '/leave/balance',
      providesTags: ['Leave'],
    }),

    // Manager/Admin endpoints for leave approvals
    getPendingLeaveRequests: builder.query<
      PaginatedResponse<LeaveRequest>,
      { page?: number }
    >({
      query: ({ page = 1 } = {}) => `/leave/pending?page=${page}`,
      providesTags: ['Leave'],
    }),

    approveLeaveRequest: builder.mutation<LeaveRequest, ApprovalAction>({
      query: ({ requestId, action, comment }) => ({
        url: `/leave/requests/${requestId}/${action}`,
        method: 'POST',
        body: { comment },
      }),
      invalidatesTags: ['Leave'],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useLoginMutation,
  useLogoutMutation,
  useGetProfileQuery,
  useUpdateProfileMutation,
  useGetAttendanceStatusQuery,
  useClockInMutation,
  useClockOutMutation,
  useGetAttendanceHistoryQuery,
  useGetAttendanceStatsQuery,
  useGetLeaveRequestsQuery,
  useCreateLeaveRequestMutation,
  useUpdateLeaveRequestMutation,
  useCancelLeaveRequestMutation,
  useGetLeaveBalanceQuery,
  useGetPendingLeaveRequestsQuery,
  useApproveLeaveRequestMutation,
} = apiSlice;
