import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { RootState } from '../store';
import {
  User,
  LoginCredentials,
  LoginResponse,
  AttendanceRecord,
  ClockActionPayload,
  AttendanceHistory,
  AttendanceStats,
  LeaveRequest,
  CreateLeaveRequest,
  LeaveBalance,
  ApprovalAction,
  ApiResponse,
  PaginatedResponse,
} from '@/types';

// Define the API base URL - this should be set in your environment variables
const API_BASE_URL =
  process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000/api';

// Mock data for development
const mockUsers = {
  '<EMAIL>': {
    id: '1',
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    role: 'admin' as const,
    department: 'IT',
    hireDate: '2023-01-01',
  },
  '<EMAIL>': {
    id: '2',
    firstName: 'Manager',
    lastName: 'User',
    email: '<EMAIL>',
    role: 'manager' as const,
    department: 'HR',
    hireDate: '2023-02-01',
  },
  '<EMAIL>': {
    id: '3',
    firstName: 'Employee',
    lastName: 'User',
    email: '<EMAIL>',
    role: 'employee' as const,
    department: 'Sales',
    hireDate: '2023-03-01',
  },
};

// Mock API function for development
const mockApiCall = <T>(data: T, delay = 1000): Promise<T> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(data), delay);
  });
};

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Add authentication token to requests
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('content-type', 'application/json');
      return headers;
    },
  }),
  tagTypes: ['User', 'Attendance', 'Leave', 'Team', 'Notification'],
  endpoints: (builder) => ({
    // Authentication endpoints
    login: builder.mutation<LoginResponse, LoginCredentials>({
      queryFn: async (credentials) => {
        try {
          // Mock authentication logic
          const user = mockUsers[credentials.email as keyof typeof mockUsers];

          if (!user || credentials.password !== 'password') {
            return {
              error: {
                status: 401,
                data: { message: 'Email ou mot de passe incorrect' },
              },
            };
          }

          const response = await mockApiCall({
            user,
            token: `mock-token-${user.id}`,
            refreshToken: `mock-refresh-token-${user.id}`,
          });

          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: 'Erreur serveur' },
            },
          };
        }
      },
    }),

    logout: builder.mutation<void, void>({
      query: () => ({
        url: '/auth/logout',
        method: 'POST',
      }),
      invalidatesTags: ['User', 'Attendance', 'Leave'],
    }),

    // User profile endpoints
    getProfile: builder.query<User, void>({
      query: () => '/user/profile',
      providesTags: ['User'],
    }),

    updateProfile: builder.mutation<User, Partial<User>>({
      query: (updates) => ({
        url: '/user/profile',
        method: 'PUT',
        body: updates,
      }),
      invalidatesTags: ['User'],
    }),

    // Attendance endpoints
    getAttendanceStatus: builder.query<AttendanceRecord | null, void>({
      queryFn: async () => {
        try {
          const mockRecord: AttendanceRecord = {
            id: '1',
            userId: '3',
            date: new Date().toISOString().split('T')[0],
            clockIn: '09:01',
            clockOut: null,
            status: 'present',
            totalTime: null,
          };

          const response = await mockApiCall(mockRecord, 500);
          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: 'Erreur lors de la récupération du statut' },
            },
          };
        }
      },
      providesTags: ['Attendance'],
    }),

    clockIn: builder.mutation<AttendanceRecord, ClockActionPayload>({
      queryFn: async (payload) => {
        try {
          const mockRecord: AttendanceRecord = {
            id: '1',
            userId: '3',
            date: new Date().toISOString().split('T')[0],
            clockIn: new Date().toLocaleTimeString('fr-FR', {
              hour: '2-digit',
              minute: '2-digit',
            }),
            clockOut: null,
            status: 'present',
            totalTime: null,
            location: payload.location,
            notes: payload.notes,
          };

          const response = await mockApiCall(mockRecord, 800);
          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: "Erreur lors du pointage d'entrée" },
            },
          };
        }
      },
      invalidatesTags: ['Attendance'],
    }),

    clockOut: builder.mutation<AttendanceRecord, ClockActionPayload>({
      queryFn: async (payload) => {
        try {
          const mockRecord: AttendanceRecord = {
            id: '1',
            userId: '3',
            date: new Date().toISOString().split('T')[0],
            clockIn: '09:01',
            clockOut: new Date().toLocaleTimeString('fr-FR', {
              hour: '2-digit',
              minute: '2-digit',
            }),
            status: 'present',
            totalTime: '8h 30min',
            location: payload.location,
            notes: payload.notes,
          };

          const response = await mockApiCall(mockRecord, 800);
          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: 'Erreur lors du pointage de sortie' },
            },
          };
        }
      },
      invalidatesTags: ['Attendance'],
    }),

    getAttendanceHistory: builder.query<
      AttendanceHistory,
      { month?: number; year?: number }
    >({
      query: ({ month, year } = {}) => {
        const params = new URLSearchParams();
        if (month) params.append('month', month.toString());
        if (year) params.append('year', year.toString());
        return `/attendance/history?${params.toString()}`;
      },
      providesTags: ['Attendance'],
    }),

    getAttendanceStats: builder.query<AttendanceStats, void>({
      query: () => '/attendance/stats',
      providesTags: ['Attendance'],
    }),

    // Leave request endpoints
    getLeaveRequests: builder.query<
      PaginatedResponse<LeaveRequest>,
      { page?: number; status?: string }
    >({
      queryFn: async ({ page = 1, status } = {}) => {
        try {
          const mockRequests: LeaveRequest[] = [
            {
              id: '1',
              userId: '3',
              type: 'vacation',
              startDate: '2024-03-15',
              endDate: '2024-03-19',
              totalDays: 5,
              status: 'pending',
              reason: 'Vacances familiales',
              comment: 'Voyage prévu depuis longtemps',
              createdAt: '2024-03-01T10:00:00Z',
              updatedAt: '2024-03-01T10:00:00Z',
            },
            {
              id: '2',
              userId: '3',
              type: 'sick',
              startDate: '2024-02-20',
              endDate: '2024-02-21',
              totalDays: 2,
              status: 'approved',
              reason: 'Grippe',
              approvedBy: '2',
              approvedAt: '2024-02-19T14:30:00Z',
              createdAt: '2024-02-19T09:00:00Z',
              updatedAt: '2024-02-19T14:30:00Z',
            },
          ];

          let filteredRequests = mockRequests;
          if (status) {
            filteredRequests = mockRequests.filter(
              (req) => req.status === status
            );
          }

          const response = await mockApiCall({
            data: filteredRequests,
            pagination: {
              page,
              limit: 10,
              total: filteredRequests.length,
              totalPages: Math.ceil(filteredRequests.length / 10),
            },
          });

          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: 'Erreur lors de la récupération des demandes' },
            },
          };
        }
      },
      providesTags: ['Leave'],
    }),

    createLeaveRequest: builder.mutation<LeaveRequest, CreateLeaveRequest>({
      queryFn: async (request) => {
        try {
          const newRequest: LeaveRequest = {
            id: Date.now().toString(),
            userId: '3',
            ...request,
            totalDays:
              Math.ceil(
                (new Date(request.endDate).getTime() -
                  new Date(request.startDate).getTime()) /
                  (1000 * 60 * 60 * 24)
              ) + 1,
            status: 'pending',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          const response = await mockApiCall(newRequest, 1000);
          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: 'Erreur lors de la création de la demande' },
            },
          };
        }
      },
      invalidatesTags: ['Leave'],
    }),

    updateLeaveRequest: builder.mutation<
      LeaveRequest,
      { id: string; updates: Partial<CreateLeaveRequest> }
    >({
      query: ({ id, updates }) => ({
        url: `/leave/requests/${id}`,
        method: 'PUT',
        body: updates,
      }),
      invalidatesTags: ['Leave'],
    }),

    cancelLeaveRequest: builder.mutation<void, string>({
      query: (id) => ({
        url: `/leave/requests/${id}/cancel`,
        method: 'POST',
      }),
      invalidatesTags: ['Leave'],
    }),

    getLeaveBalance: builder.query<LeaveBalance, void>({
      query: () => '/leave/balance',
      providesTags: ['Leave'],
    }),

    // Manager/Admin endpoints for leave approvals
    getPendingLeaveRequests: builder.query<
      PaginatedResponse<LeaveRequest>,
      { page?: number }
    >({
      query: ({ page = 1 } = {}) => `/leave/pending?page=${page}`,
      providesTags: ['Leave'],
    }),

    approveLeaveRequest: builder.mutation<LeaveRequest, ApprovalAction>({
      queryFn: async ({ requestId, action, comment }) => {
        try {
          const updatedRequest: LeaveRequest = {
            id: requestId,
            userId: '3',
            type: 'vacation',
            startDate: '2024-03-15',
            endDate: '2024-03-19',
            totalDays: 5,
            status: action === 'approve' ? 'approved' : 'rejected',
            reason: 'Vacances familiales',
            comment: 'Voyage prévu depuis longtemps',
            approvedBy: '2',
            approvedAt: new Date().toISOString(),
            rejectedReason: action === 'reject' ? comment : undefined,
            createdAt: '2024-03-01T10:00:00Z',
            updatedAt: new Date().toISOString(),
          };

          const response = await mockApiCall(updatedRequest, 800);
          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: "Erreur lors de l'approbation" },
            },
          };
        }
      },
      invalidatesTags: ['Leave'],
    }),

    // Manager Dashboard endpoints
    getManagerStats: builder.query<
      {
        todayStats: {
          totalEmployees: number;
          onLeave: number;
          absent: number;
          present: number;
        };
        weeklyStats: {
          attendanceRate: number;
          avgHoursWorked: string;
        };
        pendingRequests: number;
      },
      void
    >({
      queryFn: async () => {
        try {
          const stats = {
            todayStats: {
              totalEmployees: 15,
              onLeave: 2,
              absent: 1,
              present: 12,
            },
            weeklyStats: {
              attendanceRate: 92,
              avgHoursWorked: '7h 45min',
            },
            pendingRequests: 3,
          };

          const response = await mockApiCall(stats, 600);
          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: {
                message: 'Erreur lors de la récupération des statistiques',
              },
            },
          };
        }
      },
      providesTags: ['Team'],
    }),

    getTeamAbsences: builder.query<
      {
        onLeave: Array<{
          id: string;
          name: string;
          type: string;
          endDate: string;
        }>;
        absent: Array<{ id: string; name: string; reason: string }>;
      },
      void
    >({
      queryFn: async () => {
        try {
          const absences = {
            onLeave: [
              {
                id: '1',
                name: 'Marie Dubois',
                type: 'Congé payé',
                endDate: '2024-03-20',
              },
              {
                id: '2',
                name: 'Pierre Martin',
                type: 'Congé maladie',
                endDate: '2024-03-18',
              },
            ],
            absent: [
              {
                id: '3',
                name: 'Sophie Laurent',
                reason: 'Rendez-vous médical',
              },
            ],
          };

          const response = await mockApiCall(absences, 500);
          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: 'Erreur lors de la récupération des absences' },
            },
          };
        }
      },
      providesTags: ['Team'],
    }),

    // Admin User Management endpoints
    getAllUsers: builder.query<
      PaginatedResponse<User>,
      { page?: number; search?: string }
    >({
      queryFn: async ({ page = 1, search = '' } = {}) => {
        try {
          const allUsers: User[] = [
            {
              id: '1',
              firstName: 'Admin',
              lastName: 'User',
              email: '<EMAIL>',
              role: 'admin',
              department: 'IT',
              hireDate: '2023-01-01',
              position: 'System Administrator',
            },
            {
              id: '2',
              firstName: 'Manager',
              lastName: 'User',
              email: '<EMAIL>',
              role: 'manager',
              department: 'HR',
              hireDate: '2023-02-01',
              position: 'HR Manager',
            },
            {
              id: '3',
              firstName: 'Employee',
              lastName: 'User',
              email: '<EMAIL>',
              role: 'employee',
              department: 'Sales',
              hireDate: '2023-03-01',
              position: 'Sales Representative',
            },
            {
              id: '4',
              firstName: 'Marie',
              lastName: 'Dubois',
              email: '<EMAIL>',
              role: 'employee',
              department: 'Marketing',
              hireDate: '2023-04-01',
              position: 'Marketing Specialist',
            },
            {
              id: '5',
              firstName: 'Pierre',
              lastName: 'Martin',
              email: '<EMAIL>',
              role: 'employee',
              department: 'Development',
              hireDate: '2023-05-01',
              position: 'Software Developer',
            },
          ];

          let filteredUsers = allUsers;
          if (search) {
            filteredUsers = allUsers.filter(
              (user) =>
                user.firstName.toLowerCase().includes(search.toLowerCase()) ||
                user.lastName.toLowerCase().includes(search.toLowerCase()) ||
                user.email.toLowerCase().includes(search.toLowerCase()) ||
                user.department.toLowerCase().includes(search.toLowerCase())
            );
          }

          const response = await mockApiCall({
            data: filteredUsers,
            pagination: {
              page,
              limit: 10,
              total: filteredUsers.length,
              totalPages: Math.ceil(filteredUsers.length / 10),
            },
          });

          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: {
                message: 'Erreur lors de la récupération des utilisateurs',
              },
            },
          };
        }
      },
      providesTags: ['User'],
    }),

    createUser: builder.mutation<User, Omit<User, 'id' | 'hireDate'>>({
      queryFn: async (userData) => {
        try {
          const newUser: User = {
            ...userData,
            id: Date.now().toString(),
            hireDate: new Date().toISOString().split('T')[0],
          };

          const response = await mockApiCall(newUser, 1000);
          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: "Erreur lors de la création de l'utilisateur" },
            },
          };
        }
      },
      invalidatesTags: ['User'],
    }),

    updateUser: builder.mutation<User, { id: string; updates: Partial<User> }>({
      queryFn: async ({ id, updates }) => {
        try {
          const updatedUser: User = {
            id,
            firstName: 'Updated',
            lastName: 'User',
            email: '<EMAIL>',
            role: 'employee',
            department: 'Updated Department',
            hireDate: '2023-01-01',
            ...updates,
          };

          const response = await mockApiCall(updatedUser, 800);
          return { data: response };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: {
                message: "Erreur lors de la mise à jour de l'utilisateur",
              },
            },
          };
        }
      },
      invalidatesTags: ['User'],
    }),

    deleteUser: builder.mutation<void, string>({
      queryFn: async (id) => {
        try {
          await mockApiCall(null, 600);
          return { data: undefined };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: {
                message: "Erreur lors de la suppression de l'utilisateur",
              },
            },
          };
        }
      },
      invalidatesTags: ['User'],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useLoginMutation,
  useLogoutMutation,
  useGetProfileQuery,
  useUpdateProfileMutation,
  useGetAttendanceStatusQuery,
  useClockInMutation,
  useClockOutMutation,
  useGetAttendanceHistoryQuery,
  useGetAttendanceStatsQuery,
  useGetLeaveRequestsQuery,
  useCreateLeaveRequestMutation,
  useUpdateLeaveRequestMutation,
  useCancelLeaveRequestMutation,
  useGetLeaveBalanceQuery,
  useGetPendingLeaveRequestsQuery,
  useApproveLeaveRequestMutation,
  useGetManagerStatsQuery,
  useGetTeamAbsencesQuery,
  useGetAllUsersQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
} = apiSlice;
