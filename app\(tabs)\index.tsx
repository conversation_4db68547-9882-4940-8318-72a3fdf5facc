import { View, Text, StyleSheet, Alert } from 'react-native';
import { useState, useEffect } from 'react';
import { ScreenWrapper } from '@/components/ui/ScreenWrapper';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/hooks/useAuth';
import { useAttendance } from '@/hooks/useAttendance';
import { Clock, Calendar, CircleCheck as CheckCircle } from 'lucide-react-native';

export default function HomeScreen() {
  const { user } = useAuth();
  const { currentStatus, clockIn, clockOut, todayRecord, leaveBalance } = useAttendance();
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const handleClockAction = async () => {
    try {
      if (currentStatus === 'clocked_out') {
        await clockIn();
        Alert.alert('Succès', 'Pointage d\'entrée enregistré');
      } else {
        await clockOut();
        Alert.alert('Succès', 'Pointage de sortie enregistré');
      }
    } catch (error) {
      Alert.alert('Erreur', 'Impossible d\'enregistrer le pointage');
    }
  };

  const getButtonProps = () => {
    switch (currentStatus) {
      case 'clocked_out':
        return {
          title: 'Pointer l\'Entrée',
          variant: 'primary' as const,
          disabled: false
        };
      case 'clocked_in':
        return {
          title: 'Pointer la Sortie',
          variant: 'danger' as const,
          disabled: false
        };
      default:
        return {
          title: 'Journée terminée',
          variant: 'secondary' as const,
          disabled: true
        };
    }
  };

  const buttonProps = getButtonProps();

  return (
    <ScreenWrapper>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.greeting}>Bonjour, {user?.firstName}!</Text>
          <Text style={styles.time}>
            {currentTime.toLocaleTimeString('fr-FR', { 
              hour: '2-digit', 
              minute: '2-digit' 
            })}
          </Text>
        </View>

        <View style={styles.clockButton}>
          <Button
            title={buttonProps.title}
            variant={buttonProps.variant}
            size="large"
            disabled={buttonProps.disabled}
            onPress={handleClockAction}
          />
        </View>

        {todayRecord && (
          <Card style={styles.recordCard}>
            <View style={styles.recordRow}>
              <Clock size={20} color="#6b7280" />
              <Text style={styles.recordText}>
                {todayRecord.clockIn 
                  ? `Dernier pointage (entrée) à ${todayRecord.clockIn}`
                  : 'Aucun pointage aujourd\'hui'
                }
              </Text>
            </View>
          </Card>
        )}

        <Card style={styles.balanceCard}>
          <View style={styles.balanceRow}>
            <Calendar size={20} color="#10B981" />
            <Text style={styles.balanceText}>
              Solde de congés restants : {leaveBalance} jours
            </Text>
          </View>
        </Card>

        <Card style={styles.activityCard}>
          <Text style={styles.activityTitle}>Activités récentes</Text>
          <View style={styles.activityItem}>
            <CheckCircle size={16} color="#10B981" />
            <Text style={styles.activityText}>Pointage d'entrée - 09:01</Text>
          </View>
          <View style={styles.activityItem}>
            <CheckCircle size={16} color="#10B981" />
            <Text style={styles.activityText}>Congé validé - Hier</Text>
          </View>
        </Card>
      </View>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  greeting: {
    fontSize: 28,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 8,
  },
  time: {
    fontSize: 18,
    color: '#6b7280',
    fontWeight: '500',
  },
  clockButton: {
    marginBottom: 24,
  },
  recordCard: {
    marginBottom: 16,
  },
  recordRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recordText: {
    marginLeft: 12,
    fontSize: 16,
    color: '#374151',
  },
  balanceCard: {
    marginBottom: 16,
  },
  balanceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  balanceText: {
    marginLeft: 12,
    fontSize: 16,
    color: '#374151',
    fontWeight: '600',
  },
  activityCard: {
    marginBottom: 16,
  },
  activityTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  activityText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#6b7280',
  },
});