import { View, Text, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { useState, useEffect } from 'react';
import { ScreenWrapper } from '@/components/ui/ScreenWrapper';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useAppSelector } from '@/store/store';
import { selectCurrentUser } from '@/store/slices/authSlice';
import {
  useGetAttendanceStatusQuery,
  useClockInMutation,
  useClockOutMutation,
} from '@/store/api/apiSlice';
import {
  Clock,
  Calendar,
  CircleCheck as CheckCircle,
} from 'lucide-react-native';

export default function HomeScreen() {
  const user = useAppSelector(selectCurrentUser);
  const [currentTime, setCurrentTime] = useState(new Date());

  // RTK Query hooks
  const {
    data: todayRecord,
    isLoading: isLoadingStatus,
    error: statusError,
    refetch,
  } = useGetAttendanceStatusQuery();

  const [clockIn, { isLoading: isClockingIn }] = useClockInMutation();
  const [clockOut, { isLoading: isClockingOut }] = useClockOutMutation();

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const getCurrentStatus = () => {
    if (!todayRecord) return 'clocked_out';
    if (todayRecord.clockIn && !todayRecord.clockOut) return 'clocked_in';
    if (todayRecord.clockIn && todayRecord.clockOut) return 'completed';
    return 'clocked_out';
  };

  const currentStatus = getCurrentStatus();

  const handleClockAction = async () => {
    try {
      const payload = {
        action:
          currentStatus === 'clocked_out' ? 'clock_in' : ('clock_out' as const),
        timestamp: new Date().toISOString(),
        // You can add location data here if needed
      };

      if (currentStatus === 'clocked_out') {
        await clockIn(payload).unwrap();
        Alert.alert('Succès', "Pointage d'entrée enregistré");
      } else {
        await clockOut(payload).unwrap();
        Alert.alert('Succès', 'Pointage de sortie enregistré');
      }
    } catch (error: any) {
      console.error('Clock action error:', error);
      Alert.alert(
        'Erreur',
        error?.data?.message || "Impossible d'enregistrer le pointage"
      );
    }
  };

  const getButtonProps = () => {
    const isLoading = isClockingIn || isClockingOut;

    switch (currentStatus) {
      case 'clocked_out':
        return {
          title: "Pointer l'Entrée",
          variant: 'primary' as const,
          disabled: isLoading,
          loading: isClockingIn,
        };
      case 'clocked_in':
        return {
          title: 'Pointer la Sortie',
          variant: 'danger' as const,
          disabled: isLoading,
          loading: isClockingOut,
        };
      default:
        return {
          title: 'Journée terminée',
          variant: 'secondary' as const,
          disabled: true,
          loading: false,
        };
    }
  };

  const buttonProps = getButtonProps();

  // Show loading spinner while fetching attendance status
  if (isLoadingStatus) {
    return (
      <ScreenWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3b82f6" />
          <Text style={styles.loadingText}>Chargement...</Text>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.greeting}>Bonjour, {user?.firstName}!</Text>
          <Text style={styles.time}>
            {currentTime.toLocaleTimeString('fr-FR', {
              hour: '2-digit',
              minute: '2-digit',
            })}
          </Text>
        </View>

        <View style={styles.clockButton}>
          <Button
            title={buttonProps.title}
            variant={buttonProps.variant}
            size="large"
            disabled={buttonProps.disabled}
            onPress={handleClockAction}
          />
        </View>

        {todayRecord && (
          <Card style={styles.recordCard}>
            <View style={styles.recordRow}>
              <Clock size={20} color="#6b7280" />
              <Text style={styles.recordText}>
                {todayRecord.clockIn
                  ? `Dernier pointage (entrée) à ${todayRecord.clockIn}`
                  : "Aucun pointage aujourd'hui"}
              </Text>
            </View>
          </Card>
        )}

        <Card style={styles.balanceCard}>
          <View style={styles.balanceRow}>
            <Calendar size={20} color="#10B981" />
            <Text style={styles.balanceText}>
              Solde de congés restants : 15 jours
            </Text>
          </View>
        </Card>

        <Card style={styles.activityCard}>
          <Text style={styles.activityTitle}>Activités récentes</Text>
          <View style={styles.activityItem}>
            <CheckCircle size={16} color="#10B981" />
            <Text style={styles.activityText}>Pointage d'entrée - 09:01</Text>
          </View>
          <View style={styles.activityItem}>
            <CheckCircle size={16} color="#10B981" />
            <Text style={styles.activityText}>Congé validé - Hier</Text>
          </View>
        </Card>
      </View>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  greeting: {
    fontSize: 28,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 8,
  },
  time: {
    fontSize: 18,
    color: '#6b7280',
    fontWeight: '500',
  },
  clockButton: {
    marginBottom: 24,
  },
  recordCard: {
    marginBottom: 16,
  },
  recordRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recordText: {
    marginLeft: 12,
    fontSize: 16,
    color: '#374151',
  },
  balanceCard: {
    marginBottom: 16,
  },
  balanceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  balanceText: {
    marginLeft: 12,
    fontSize: 16,
    color: '#374151',
    fontWeight: '600',
  },
  activityCard: {
    marginBottom: 16,
  },
  activityTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  activityText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#6b7280',
  },
});
