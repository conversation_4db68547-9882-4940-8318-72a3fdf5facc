import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useState, useEffect } from 'react';
import { ScreenWrapper } from '@/components/ui/ScreenWrapper';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { InputField } from '@/components/ui/InputField';
import { Modal } from '@/components/ui/Modal';
import {
  useGetAllUsersQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
} from '@/store/api/apiSlice';
import { User as UserType } from '@/types/auth';
import {
  Plus,
  Search,
  User,
  Mail,
  Briefcase,
  MoveVertical as MoreVertical,
} from 'lucide-react-native';

export default function UserManagementScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    department: '',
    role: 'employee' as const,
    position: '',
  });

  // RTK Query hooks
  const {
    data: usersData,
    isLoading: isLoadingUsers,
    error: usersError,
    refetch,
  } = useGetAllUsersQuery({ search: searchQuery });

  const [createUser, { isLoading: isCreating }] = useCreateUserMutation();
  const [updateUser, { isLoading: isUpdating }] = useUpdateUserMutation();
  const [deleteUser, { isLoading: isDeleting }] = useDeleteUserMutation();

  const users = usersData?.data || [];

  const filteredUsers = users.filter(
    (user) =>
      user.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSubmit = async () => {
    if (
      !formData.firstName ||
      !formData.lastName ||
      !formData.email ||
      !formData.department
    ) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs obligatoires');
      return;
    }

    try {
      if (selectedUser) {
        await updateUser({
          id: selectedUser.id,
          updates: formData,
        }).unwrap();
        Alert.alert('Succès', 'Utilisateur mis à jour avec succès');
      } else {
        await createUser(formData).unwrap();
        Alert.alert('Succès', 'Utilisateur créé avec succès');
      }
      setShowModal(false);
      resetForm();
    } catch (error: any) {
      console.error('Erreur:', error);
      Alert.alert(
        'Erreur',
        error?.data?.message || "Impossible de sauvegarder l'utilisateur"
      );
    }
  };

  const handleDelete = (userId: string, userName: string) => {
    Alert.alert(
      'Confirmer la suppression',
      `Êtes-vous sûr de vouloir supprimer ${userName} ?`,
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteUser(userId).unwrap();
              Alert.alert('Succès', 'Utilisateur supprimé avec succès');
            } catch (error: any) {
              console.error('Delete error:', error);
              Alert.alert(
                'Erreur',
                error?.data?.message || "Impossible de supprimer l'utilisateur"
              );
            }
          },
        },
      ]
    );
  };

  const resetForm = () => {
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      department: '',
      role: 'employee',
      position: '',
    });
    setSelectedUser(null);
  };

  const handleEdit = (user: UserType) => {
    setSelectedUser(user);
    setFormData({
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      department: user.department,
      role: user.role,
      position: user.position || '',
    });
    setShowModal(true);
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'danger';
      case 'manager':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Admin';
      case 'manager':
        return 'Manager';
      default:
        return 'Employé';
    }
  };

  const renderUser = ({ item }: { item: any }) => (
    <Card style={styles.userCard}>
      <View style={styles.userHeader}>
        <View style={styles.userInfo}>
          <View style={styles.avatar}>
            <User size={20} color="#ffffff" />
          </View>
          <View style={styles.userDetails}>
            <Text style={styles.userName}>
              {item.firstName} {item.lastName}
            </Text>
            <View style={styles.userMeta}>
              <Mail size={14} color="#6b7280" />
              <Text style={styles.userEmail}>{item.email}</Text>
            </View>
            <View style={styles.userMeta}>
              <Briefcase size={14} color="#6b7280" />
              <Text style={styles.userDepartment}>{item.department}</Text>
            </View>
          </View>
        </View>
        <View style={styles.userActions}>
          <Badge
            variant={getRoleBadgeVariant(item.role)}
            text={getRoleLabel(item.role)}
          />
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleEdit(item)}
          >
            <MoreVertical size={20} color="#6b7280" />
          </TouchableOpacity>
        </View>
      </View>
    </Card>
  );

  // Show loading state while fetching users
  if (isLoadingUsers) {
    return (
      <ScreenWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3b82f6" />
          <Text style={styles.loadingText}>Chargement des utilisateurs...</Text>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Gestion des Utilisateurs</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowModal(true)}
          >
            <Plus size={24} color="#ffffff" />
          </TouchableOpacity>
        </View>

        <View style={styles.searchContainer}>
          <View style={styles.searchWrapper}>
            <Search size={20} color="#6b7280" />
            <InputField
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Rechercher un utilisateur..."
              style={styles.searchInput}
            />
          </View>
        </View>

        {users.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Aucun utilisateur trouvé</Text>
          </View>
        ) : (
          <FlatList
            data={users}
            renderItem={renderUser}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        )}

        <Modal
          visible={showModal}
          onClose={() => {
            setShowModal(false);
            resetForm();
          }}
          title={selectedUser ? "Modifier l'utilisateur" : 'Nouvel utilisateur'}
        >
          <View style={styles.modalContent}>
            <InputField
              label="Prénom"
              value={formData.firstName}
              onChangeText={(text) =>
                setFormData({ ...formData, firstName: text })
              }
              placeholder="Prénom"
            />
            <InputField
              label="Nom"
              value={formData.lastName}
              onChangeText={(text) =>
                setFormData({ ...formData, lastName: text })
              }
              placeholder="Nom"
            />
            <InputField
              label="Email"
              value={formData.email}
              onChangeText={(text) => setFormData({ ...formData, email: text })}
              placeholder="<EMAIL>"
              keyboardType="email-address"
            />
            <InputField
              label="Département"
              value={formData.department}
              onChangeText={(text) =>
                setFormData({ ...formData, department: text })
              }
              placeholder="Département"
            />
            <InputField
              label="Rôle"
              value={formData.role}
              onChangeText={(text) => setFormData({ ...formData, role: text })}
              placeholder="employee | manager | admin"
            />
            <Button
              title={selectedUser ? 'Modifier' : 'Créer'}
              onPress={handleSubmit}
              loading={loading}
              style={styles.submitButton}
            />
          </View>
        </Modal>
      </View>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#111827',
  },
  addButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#3B82F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  searchWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: '#111827',
  },
  listContainer: {
    padding: 16,
  },
  userCard: {
    marginBottom: 12,
  },
  userHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  userInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#3B82F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  userMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  userEmail: {
    marginLeft: 6,
    fontSize: 14,
    color: '#6b7280',
  },
  userDepartment: {
    marginLeft: 6,
    fontSize: 14,
    color: '#6b7280',
  },
  userActions: {
    alignItems: 'flex-end',
  },
  actionButton: {
    marginTop: 8,
    padding: 4,
  },
  modalContent: {
    gap: 16,
  },
  submitButton: {
    marginTop: 8,
  },
});
