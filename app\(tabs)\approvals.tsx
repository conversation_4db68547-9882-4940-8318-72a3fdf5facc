import { View, Text, StyleSheet, FlatList, Alert } from 'react-native';
import { ScreenWrapper } from '@/components/ui/ScreenWrapper';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useApprovals } from '@/hooks/useApprovals';
import { Calendar, MessageCircle, User } from 'lucide-react-native';

export default function ApprovalsScreen() {
  const { pendingRequests, approveRequest, rejectRequest, loading } = useApprovals();

  const handleApprove = (requestId: string) => {
    Alert.alert(
      'Confirmer',
      'Voulez-vous approuver cette demande de congé ?',
      [
        { text: 'Annuler', style: 'cancel' },
        { text: 'Approuver', onPress: () => approveRequest(requestId) }
      ]
    );
  };

  const handleReject = (requestId: string) => {
    Alert.prompt(
      'Refuser la demande',
      'Veuillez indiquer la raison du refus :',
      [
        { text: 'Annuler', style: 'cancel' },
        { 
          text: 'Refuser', 
          onPress: (reason) => rejectRequest(requestId, reason || 'Aucune raison spécifiée')
        }
      ],
      'plain-text'
    );
  };

  const renderRequest = ({ item }: { item: any }) => (
    <Card style={styles.requestCard}>
      <View style={styles.requestHeader}>
        <View style={styles.employeeInfo}>
          <View style={styles.avatar}>
            <User size={20} color="#ffffff" />
          </View>
          <View>
            <Text style={styles.employeeName}>
              {item.employee.firstName} {item.employee.lastName}
            </Text>
            <Text style={styles.employeeRole}>{item.employee.department}</Text>
          </View>
        </View>
      </View>

      <View style={styles.requestDetails}>
        <View style={styles.detailRow}>
          <Calendar size={16} color="#6b7280" />
          <Text style={styles.detailText}>
            {item.type} - Du {item.startDate} au {item.endDate}
          </Text>
        </View>

        {item.comment && (
          <View style={styles.detailRow}>
            <MessageCircle size={16} color="#6b7280" />
            <Text style={styles.detailText}>{item.comment}</Text>
          </View>
        )}

        <Text style={styles.daysCount}>
          Durée: {item.days} jour{item.days > 1 ? 's' : ''}
        </Text>
      </View>

      <View style={styles.actionButtons}>
        <Button
          title="Refuser"
          variant="danger"
          size="small"
          onPress={() => handleReject(item.id)}
          disabled={loading}
          style={styles.actionButton}
        />
        <Button
          title="Approuver"
          variant="primary"
          size="small"
          onPress={() => handleApprove(item.id)}
          disabled={loading}
          style={styles.actionButton}
        />
      </View>
    </Card>
  );

  return (
    <ScreenWrapper>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Approbations</Text>
          <Text style={styles.subtitle}>
            {pendingRequests.length} demande{pendingRequests.length > 1 ? 's' : ''} en attente
          </Text>
        </View>

        {pendingRequests.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>Aucune demande en attente</Text>
            <Text style={styles.emptySubtext}>
              Toutes les demandes ont été traitées
            </Text>
          </View>
        ) : (
          <FlatList
            data={pendingRequests}
            renderItem={renderRequest}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#111827',
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  listContainer: {
    padding: 16,
  },
  requestCard: {
    marginBottom: 16,
  },
  requestHeader: {
    marginBottom: 12,
  },
  employeeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#3B82F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  employeeName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  employeeRole: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  requestDetails: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  detailText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#374151',
    flex: 1,
  },
  daysCount: {
    fontSize: 14,
    fontWeight: '600',
    color: '#3B82F6',
    marginTop: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6b7280',
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#9ca3af',
    textAlign: 'center',
    marginTop: 8,
  },
});