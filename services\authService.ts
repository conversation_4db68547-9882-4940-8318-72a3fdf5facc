import { store } from '@/store/store';
import { setCredentials, logOut, restoreAuth, setLoading } from '@/store/slices/authSlice';
import { StorageService } from './storage';
import { User } from '@/types/auth';

export class AuthService {
  /**
   * Initialize authentication state from stored data
   * This should be called when the app starts
   */
  static async initializeAuth(): Promise<void> {
    store.dispatch(setLoading(true));
    
    try {
      const authData = await StorageService.getAuthData();
      store.dispatch(restoreAuth(authData));
    } catch (error) {
      console.error('Error initializing auth:', error);
      store.dispatch(restoreAuth(null));
    }
  }

  /**
   * Save authentication data after successful login
   */
  static async saveAuthData(user: User, token: string, refreshToken?: string): Promise<void> {
    try {
      // Save to secure storage
      await StorageService.saveAuthData(user, token, refreshToken);
      
      // Update Redux state
      store.dispatch(setCredentials({ user, token }));
    } catch (error) {
      console.error('Error saving auth data:', error);
      throw new Error('Failed to save authentication data');
    }
  }

  /**
   * Clear authentication data and logout
   */
  static async logout(): Promise<void> {
    try {
      // Clear from secure storage
      await StorageService.clearAuthData();
      
      // Update Redux state
      store.dispatch(logOut());
    } catch (error) {
      console.error('Error during logout:', error);
      // Even if storage clearing fails, we should still logout from Redux
      store.dispatch(logOut());
      throw new Error('Failed to logout completely');
    }
  }

  /**
   * Get current authentication token
   */
  static async getToken(): Promise<string | null> {
    // First try to get from Redux state (faster)
    const state = store.getState();
    if (state.auth.token) {
      return state.auth.token;
    }

    // Fallback to storage
    return await StorageService.getToken();
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    const state = store.getState();
    return state.auth.isAuthenticated;
  }

  /**
   * Get current user
   */
  static getCurrentUser(): User | null {
    const state = store.getState();
    return state.auth.user;
  }

  /**
   * Check if current user has specific role
   */
  static hasRole(role: 'employee' | 'manager' | 'admin'): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;

    // Admin has access to everything
    if (user.role === 'admin') return true;
    
    // Manager has access to manager and employee features
    if (user.role === 'manager' && (role === 'manager' || role === 'employee')) return true;
    
    // Employee only has access to employee features
    if (user.role === 'employee' && role === 'employee') return true;

    return false;
  }

  /**
   * Check if current user can access a specific feature
   */
  static canAccess(feature: 'dashboard' | 'approvals' | 'users' | 'analytics'): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;

    switch (feature) {
      case 'dashboard':
        return this.hasRole('manager') || this.hasRole('admin');
      case 'approvals':
        return this.hasRole('manager') || this.hasRole('admin');
      case 'users':
        return this.hasRole('admin');
      case 'analytics':
        return this.hasRole('admin');
      default:
        return false;
    }
  }

  /**
   * Refresh authentication token
   * This would typically be called when the current token is about to expire
   */
  static async refreshToken(): Promise<boolean> {
    try {
      const authData = await StorageService.getAuthData();
      if (!authData?.refreshToken) {
        return false;
      }

      // Here you would make an API call to refresh the token
      // For now, we'll just return false to indicate refresh is not implemented
      // TODO: Implement token refresh logic with your backend
      
      return false;
    } catch (error) {
      console.error('Error refreshing token:', error);
      return false;
    }
  }
}
