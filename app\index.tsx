import { useEffect, useState } from 'react';
import { router } from 'expo-router';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { useAppSelector } from '@/store/store';
import {
  selectIsAuthenticated,
  selectAuthLoading,
} from '@/store/slices/authSlice';

export default function IndexScreen() {
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const isLoading = useAppSelector(selectAuthLoading);
  const [navigationReady, setNavigationReady] = useState(false);

  useEffect(() => {
    // Mark navigation as ready after a short delay
    const timer = setTimeout(() => {
      setNavigationReady(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Don't navigate while loading or if navigation isn't ready
    if (isLoading || !navigationReady) return;

    const timer = setTimeout(() => {
      if (isAuthenticated) {
        router.replace('/(tabs)');
      } else {
        router.replace('/login');
      }
    }, 100); // Small delay to ensure navigation is ready

    return () => clearTimeout(timer);
  }, [isAuthenticated, isLoading, navigationReady]);

  // Show loading screen while initializing auth
  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color="#3b82f6" />
      <Text style={styles.text}>Chargement...</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
  },
  text: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
    fontWeight: '500',
  },
});
